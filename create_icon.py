#!/usr/bin/env python3
"""
Create a simple icon file for Tauri
"""
from PIL import Image, ImageDraw
import os

def create_icon():
    # Create a simple 512x512 image
    size = 512
    img = Image.new('RGBA', (size, size), (76, 175, 80, 255))  # Green background
    
    # Draw a simple "TG" text
    draw = ImageDraw.Draw(img)
    
    # Draw a circle
    margin = 50
    draw.ellipse([margin, margin, size-margin, size-margin], fill=(255, 255, 255, 255))
    
    # Save as PNG first
    img.save('app-icon.png')
    
    # Create different sizes for Tauri
    sizes = [32, 128, 256, 512]
    
    # Create icons directory if it doesn't exist
    os.makedirs('src-tauri/icons', exist_ok=True)
    
    for s in sizes:
        resized = img.resize((s, s), Image.Resampling.LANCZOS)
        if s == 32:
            resized.save('src-tauri/icons/32x32.png')
        elif s == 128:
            resized.save('src-tauri/icons/128x128.png')
            resized.save('src-tauri/icons/<EMAIL>')
    
    # Save as ICO
    img.save('src-tauri/icons/icon.ico', format='ICO', sizes=[(32, 32), (64, 64), (128, 128), (256, 256)])
    
    # For macOS (ICNS would need additional tools)
    img.save('src-tauri/icons/icon.icns', format='ICNS')
    
    print("Icons created successfully!")

if __name__ == "__main__":
    create_icon()
