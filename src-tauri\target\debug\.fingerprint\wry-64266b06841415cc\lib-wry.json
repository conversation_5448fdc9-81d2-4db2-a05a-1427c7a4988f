{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 9546548099588661534, "deps": [[3007252114546291461, "tao", false, 7397381523915221033], [3150220818285335163, "url", false, 4649323851504239578], [3540822385484940109, "windows_implement", false, 4408266691572968263], [3722963349756955755, "once_cell", false, 6564127573579152074], [4381063397040571828, "webview2_com", false, 1518854012022457691], [4405182208873388884, "http", false, 2680188079743080968], [4684437522915235464, "libc", false, 9316783122572719906], [5986029879202738730, "log", false, 9663051989621911511], [7653476968652377684, "windows", false, 11044908766836782643], [8008191657135824715, "thiserror", false, 11176856808456277608], [8391357152270261188, "build_script_build", false, 5553815382231770682], [8569119365930580996, "serde_json", false, 6500556973627130142], [9689903380558560274, "serde", false, 18184860513468081355], [11989259058781683633, "dunce", false, 16815482336677619011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-64266b06841415cc\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}