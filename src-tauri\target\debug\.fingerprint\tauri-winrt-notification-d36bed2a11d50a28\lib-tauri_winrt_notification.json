{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 10434116139574476539, "deps": [[1462335029370885857, "quick_xml", false, 15939009189403879951], [3334271191048661305, "windows_version", false, 7221431710029169125], [10806645703491011684, "thiserror", false, 14914600585008300939], [14585479307175734061, "windows", false, 9579286246398003101]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-d36bed2a11d50a28\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}